import { useState } from "react";
import PageHeader from "@/components/common/PageHeader";
import LocationFilterDropdown from "@/components/common/LocationFilterDropdown";
import MetricCard from "@/components/Charts/MetricCard";
import { LocationFilterState } from "@/types/locationFilter";
import {
  Shield, AlertTriangle, Clock, CheckCircle, Target, TrendingUp, Award, BarChart3,
  Leaf, TreePine, Zap, Users, Settings, Cog, FileCheck, BookOpen,
  Heart, Truck, AlertOctagon, Cpu, Lightbulb
} from "lucide-react";


const HomePage = () => {
  // Location filter state
  const [locationFilters, setLocationFilters] = useState<LocationFilterState>({
    locationOne: '',
    locationTwo: '',
    locationThree: '',
    locationFour: '',
    locationFive: '',
    locationSix: '',
  });

  // Handle location filter changes
  const handleLocationFilterChange = (filters: LocationFilterState) => {
    setLocationFilters(filters);
    // Here you can add logic to filter data based on location
    console.log('Location filters applied:', filters);
  };

  // Performance Metrics Data - Structured for easy API integration
  const getPerformanceMetrics = () => {
    // Safety Performance Metrics
    const safetyMetrics = [
      {
        id: "safety-incidents",
        title: "Safety Incidents",
        value: "2",
        unit: "incidents",
        targetPercentage: -15.2,
        trend: [8, 6, 5, 4, 3, 2, 2, 2, 2, 2, 2, 2],
        isImproving: true
      },
      {
        id: "days-without-incident",
        title: "Days Without Incident",
        value: "127",
        unit: "days",
        targetPercentage: 12.5,
        trend: [45, 67, 89, 102, 115, 127, 127, 127, 127, 127, 127, 127],
        isImproving: true
      },
      {
        id: "safety-training-completion",
        title: "Safety Training Completion",
        value: "94.8",
        unit: "%",
        targetPercentage: 5.3,
        trend: [85, 87, 89, 91, 92, 93, 94, 94.2, 94.5, 94.6, 94.7, 94.8],
        isImproving: true
      },
      {
        id: "near-miss-reports",
        title: "Near Miss Reports",
        value: "18",
        unit: "reports",
        targetPercentage: 28.6,
        trend: [8, 10, 12, 14, 15, 16, 17, 17, 18, 18, 18, 18],
        isImproving: true
      }
    ];

    // Quality Performance Metrics
    const qualityMetrics = [
      {
        id: "first-pass-yield",
        title: "First Pass Yield",
        value: "96.7",
        unit: "%",
        targetPercentage: 8.2,
        trend: [89, 91, 92, 93, 94, 95, 95.5, 96, 96.2, 96.4, 96.6, 96.7],
        isImproving: true
      },
      {
        id: "defect-rate",
        title: "Defect Rate",
        value: "0.8",
        unit: "%",
        targetPercentage: -23.1,
        trend: [2.1, 1.9, 1.7, 1.5, 1.3, 1.1, 1.0, 0.9, 0.85, 0.82, 0.81, 0.8],
        isImproving: true
      },
      {
        id: "customer-satisfaction",
        title: "Customer Satisfaction",
        value: "4.6",
        unit: "/5.0",
        targetPercentage: 15.0,
        trend: [4.0, 4.1, 4.2, 4.2, 4.3, 4.4, 4.4, 4.5, 4.5, 4.6, 4.6, 4.6],
        isImproving: true
      },
      {
        id: "process-capability",
        title: "Process Capability (Cpk)",
        value: "1.67",
        unit: "index",
        targetPercentage: 11.3,
        trend: [1.2, 1.3, 1.35, 1.4, 1.45, 1.5, 1.55, 1.6, 1.62, 1.65, 1.66, 1.67],
        isImproving: true
      }
    ];

    // Environmental Performance Metrics
    const environmentalMetrics = [
      {
        id: "carbon-footprint",
        title: "Carbon Footprint",
        value: "2,847",
        unit: "tCO2e",
        targetPercentage: -12.4,
        trend: [3200, 3150, 3100, 3050, 3000, 2950, 2900, 2870, 2860, 2850, 2848, 2847],
        isImproving: true
      },
      {
        id: "energy-efficiency",
        title: "Energy Efficiency",
        value: "87.3",
        unit: "%",
        targetPercentage: 9.7,
        trend: [79, 81, 82, 83, 84, 85, 86, 86.5, 87, 87.1, 87.2, 87.3],
        isImproving: true
      },
      {
        id: "waste-reduction",
        title: "Waste Reduction",
        value: "23.8",
        unit: "%",
        targetPercentage: 18.9,
        trend: [10, 12, 14, 16, 18, 19, 20, 21, 22, 23, 23.5, 23.8],
        isImproving: true
      },
      {
        id: "water-conservation",
        title: "Water Conservation",
        value: "31.2",
        unit: "%",
        targetPercentage: 24.0,
        trend: [15, 17, 19, 21, 23, 25, 27, 28, 29, 30, 31, 31.2],
        isImproving: true
      }
    ];

    // Risk Management Effectiveness Metrics
    const riskManagementMetrics = [
      {
        id: "risk-identification-rate",
        title: "Risk Identification Rate",
        value: "94.6",
        unit: "%",
        targetPercentage: 8.3,
        trend: [85, 87, 88, 89, 90, 91, 92, 93, 94, 94.2, 94.4, 94.6],
        isImproving: true
      },
      {
        id: "mitigation-effectiveness",
        title: "Mitigation Effectiveness",
        value: "89.7",
        unit: "%",
        targetPercentage: 12.6,
        trend: [78, 80, 82, 84, 85, 86, 87, 88, 89, 89.3, 89.5, 89.7],
        isImproving: true
      },
      {
        id: "risk-closure-time",
        title: "Risk Closure Time",
        value: "14.2",
        unit: "days",
        targetPercentage: -22.8,
        trend: [22, 21, 20, 19, 18, 17, 16, 15.5, 15, 14.8, 14.5, 14.2],
        isImproving: true
      },
      {
        id: "residual-risk-score",
        title: "Residual Risk Score",
        value: "2.3",
        unit: "/10",
        targetPercentage: -18.5,
        trend: [3.5, 3.3, 3.1, 2.9, 2.8, 2.7, 2.6, 2.5, 2.4, 2.35, 2.32, 2.3],
        isImproving: true
      }
    ];

    // Operational Discipline Metrics
    const operationalDisciplineMetrics = [
      {
        id: "procedure-adherence",
        title: "Procedure Adherence",
        value: "96.8",
        unit: "%",
        targetPercentage: 7.8,
        trend: [88, 90, 91, 92, 93, 94, 95, 96, 96.3, 96.5, 96.7, 96.8],
        isImproving: true
      },
      {
        id: "task-completion-rate",
        title: "Task Completion Rate",
        value: "98.4",
        unit: "%",
        targetPercentage: 4.2,
        trend: [93, 94, 95, 96, 97, 97.5, 98, 98.1, 98.2, 98.3, 98.35, 98.4],
        isImproving: true
      },
      {
        id: "deviation-incidents",
        title: "Deviation Incidents",
        value: "3",
        unit: "incidents",
        targetPercentage: -40.0,
        trend: [8, 7, 6, 5, 5, 4, 4, 3, 3, 3, 3, 3],
        isImproving: true
      },
      {
        id: "audit-compliance",
        title: "Audit Compliance",
        value: "92.1",
        unit: "%",
        targetPercentage: 15.6,
        trend: [78, 80, 82, 84, 86, 88, 89, 90, 91, 91.5, 91.8, 92.1],
        isImproving: true
      }
    ];

    // Process Reliability & Efficiency Metrics
    const processReliabilityMetrics = [
      {
        id: "equipment-uptime",
        title: "Equipment Uptime",
        value: "97.2",
        unit: "%",
        targetPercentage: 3.8,
        trend: [92, 93, 94, 95, 95.5, 96, 96.5, 97, 97.1, 97.15, 97.18, 97.2],
        isImproving: true
      },
      {
        id: "process-efficiency",
        title: "Process Efficiency",
        value: "89.6",
        unit: "%",
        targetPercentage: 11.2,
        trend: [78, 80, 82, 84, 85, 86, 87, 88, 89, 89.3, 89.5, 89.6],
        isImproving: true
      },
      {
        id: "throughput-rate",
        title: "Throughput Rate",
        value: "1,247",
        unit: "units/hr",
        targetPercentage: 8.9,
        trend: [1100, 1120, 1140, 1160, 1180, 1200, 1220, 1230, 1240, 1243, 1245, 1247],
        isImproving: true
      },
      {
        id: "cycle-time-variance",
        title: "Cycle Time Variance",
        value: "4.2",
        unit: "%",
        targetPercentage: -28.8,
        trend: [7.5, 7.0, 6.5, 6.0, 5.5, 5.0, 4.8, 4.6, 4.4, 4.3, 4.25, 4.2],
        isImproving: true
      }
    ];

    // Compliance & Regulatory Adherence Metrics
    const complianceMetrics = [
      {
        id: "regulatory-compliance",
        title: "Regulatory Compliance",
        value: "98.7",
        unit: "%",
        targetPercentage: 2.1,
        trend: [95, 96, 96.5, 97, 97.5, 98, 98.2, 98.4, 98.5, 98.6, 98.65, 98.7],
        isImproving: true
      },
      {
        id: "permit-renewals",
        title: "Permit Renewals",
        value: "100",
        unit: "%",
        targetPercentage: 0.0,
        trend: [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100],
        isImproving: true
      },
      {
        id: "audit-findings",
        title: "Audit Findings",
        value: "2",
        unit: "findings",
        targetPercentage: -60.0,
        trend: [8, 7, 6, 5, 4, 3, 3, 2, 2, 2, 2, 2],
        isImproving: true
      },
      {
        id: "compliance-training",
        title: "Compliance Training",
        value: "95.3",
        unit: "%",
        targetPercentage: 6.7,
        trend: [87, 89, 90, 91, 92, 93, 94, 94.5, 95, 95.1, 95.2, 95.3],
        isImproving: true
      }
    ];

    // Organizational Competence & Learning Metrics
    const organizationalCompetenceMetrics = [
      {
        id: "knowledge-index",
        title: "Knowledge Index",
        value: "78.4",
        unit: "index",
        targetPercentage: 12.3,
        trend: [68, 70, 72, 74, 75, 76, 77, 77.5, 78, 78.2, 78.3, 78.4],
        isImproving: true
      },
      {
        id: "reactivation-index",
        title: "Reactivation Index",
        value: "85.7",
        unit: "index",
        targetPercentage: 8.9,
        trend: [76, 78, 80, 81, 82, 83, 84, 84.5, 85, 85.3, 85.5, 85.7],
        isImproving: true
      },
      {
        id: "training-effectiveness",
        title: "Training Effectiveness",
        value: "91.2",
        unit: "%",
        targetPercentage: 14.5,
        trend: [78, 80, 82, 84, 86, 87, 88, 89, 90, 90.5, 90.8, 91.2],
        isImproving: true
      },
      {
        id: "skill-gap-closure",
        title: "Skill Gap Closure",
        value: "73.8",
        unit: "%",
        targetPercentage: 23.4,
        trend: [55, 58, 61, 64, 66, 68, 70, 71, 72, 73, 73.5, 73.8],
        isImproving: true
      }
    ];

    // Leadership Engagement & Culture Metrics
    const leadershipEngagementMetrics = [
      {
        id: "leadership-visibility",
        title: "Leadership Visibility",
        value: "87.3",
        unit: "%",
        targetPercentage: 16.8,
        trend: [72, 74, 76, 78, 80, 82, 84, 85, 86, 86.5, 87, 87.3],
        isImproving: true
      },
      {
        id: "culture-alignment",
        title: "Culture Alignment",
        value: "82.6",
        unit: "%",
        targetPercentage: 10.4,
        trend: [73, 75, 77, 78, 79, 80, 81, 81.5, 82, 82.2, 82.4, 82.6],
        isImproving: true
      },
      {
        id: "employee-feedback",
        title: "Employee Feedback",
        value: "4.3",
        unit: "/5.0",
        targetPercentage: 13.2,
        trend: [3.6, 3.7, 3.8, 3.9, 4.0, 4.1, 4.1, 4.2, 4.2, 4.25, 4.28, 4.3],
        isImproving: true
      },
      {
        id: "communication-effectiveness",
        title: "Communication Effectiveness",
        value: "79.4",
        unit: "%",
        targetPercentage: 18.7,
        trend: [65, 67, 69, 71, 73, 75, 76, 77, 78, 78.5, 79, 79.4],
        isImproving: true
      }
    ];

    // Employee Engagement & Wellbeing Metrics
    const employeeEngagementMetrics = [
      {
        id: "engagement-score",
        title: "Engagement Score",
        value: "76.8",
        unit: "%",
        targetPercentage: 15.3,
        trend: [64, 66, 68, 70, 72, 73, 74, 75, 76, 76.3, 76.6, 76.8],
        isImproving: true
      },
      {
        id: "wellbeing-index",
        title: "Wellbeing Index",
        value: "81.2",
        unit: "index",
        targetPercentage: 12.7,
        trend: [70, 72, 74, 76, 77, 78, 79, 80, 80.5, 80.8, 81, 81.2],
        isImproving: true
      },
      {
        id: "retention-rate",
        title: "Retention Rate",
        value: "94.6",
        unit: "%",
        targetPercentage: 5.8,
        trend: [88, 89, 90, 91, 92, 93, 93.5, 94, 94.2, 94.3, 94.5, 94.6],
        isImproving: true
      },
      {
        id: "work-life-balance",
        title: "Work-Life Balance",
        value: "4.1",
        unit: "/5.0",
        targetPercentage: 17.1,
        trend: [3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 4.0, 4.05, 4.08, 4.09, 4.1],
        isImproving: true
      }
    ];

    // Supply Chain & Contractor Performance Metrics
    const supplyChainMetrics = [
      {
        id: "supplier-performance",
        title: "Supplier Performance",
        value: "88.4",
        unit: "%",
        targetPercentage: 10.6,
        trend: [78, 80, 82, 84, 85, 86, 87, 87.5, 88, 88.2, 88.3, 88.4],
        isImproving: true
      },
      {
        id: "contractor-safety",
        title: "Contractor Safety",
        value: "96.2",
        unit: "%",
        targetPercentage: 4.3,
        trend: [91, 92, 93, 94, 95, 95.5, 96, 96.1, 96.15, 96.18, 96.19, 96.2],
        isImproving: true
      },
      {
        id: "delivery-performance",
        title: "Delivery Performance",
        value: "92.7",
        unit: "%",
        targetPercentage: 7.8,
        trend: [84, 86, 87, 88, 89, 90, 91, 91.5, 92, 92.3, 92.5, 92.7],
        isImproving: true
      },
      {
        id: "cost-efficiency",
        title: "Cost Efficiency",
        value: "15.3",
        unit: "% savings",
        targetPercentage: 22.4,
        trend: [8, 9, 10, 11, 12, 13, 14, 14.5, 15, 15.1, 15.2, 15.3],
        isImproving: true
      }
    ];

    // Crisis Preparedness & Business Continuity Metrics
    const crisisPreparednessMetrics = [
      {
        id: "emergency-response-time",
        title: "Emergency Response Time",
        value: "3.2",
        unit: "minutes",
        targetPercentage: -36.0,
        trend: [6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.8, 3.6, 3.4, 3.3, 3.25, 3.2],
        isImproving: true
      },
      {
        id: "drill-effectiveness",
        title: "Drill Effectiveness",
        value: "94.8",
        unit: "%",
        targetPercentage: 8.6,
        trend: [85, 87, 88, 89, 90, 91, 92, 93, 94, 94.3, 94.6, 94.8],
        isImproving: true
      },
      {
        id: "business-continuity",
        title: "Business Continuity",
        value: "97.1",
        unit: "%",
        targetPercentage: 2.8,
        trend: [93, 94, 95, 95.5, 96, 96.3, 96.6, 96.8, 97, 97.05, 97.08, 97.1],
        isImproving: true
      },
      {
        id: "recovery-time",
        title: "Recovery Time",
        value: "2.4",
        unit: "hours",
        targetPercentage: -52.0,
        trend: [6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0, 2.8, 2.6, 2.5, 2.45, 2.4],
        isImproving: true
      }
    ];

    // Digital Maturity & Automation Metrics
    const digitalMaturityMetrics = [
      {
        id: "automation-level",
        title: "Automation Level",
        value: "67.3",
        unit: "%",
        targetPercentage: 24.8,
        trend: [45, 48, 51, 54, 57, 60, 62, 64, 65, 66, 66.5, 67.3],
        isImproving: true
      },
      {
        id: "digital-adoption",
        title: "Digital Adoption",
        value: "82.6",
        unit: "%",
        targetPercentage: 18.4,
        trend: [65, 68, 70, 72, 74, 76, 78, 79, 80, 81, 82, 82.6],
        isImproving: true
      },
      {
        id: "system-integration",
        title: "System Integration",
        value: "74.9",
        unit: "%",
        targetPercentage: 31.6,
        trend: [50, 53, 56, 59, 62, 65, 68, 70, 72, 73, 74, 74.9],
        isImproving: true
      },
      {
        id: "data-quality",
        title: "Data Quality",
        value: "91.4",
        unit: "%",
        targetPercentage: 14.2,
        trend: [78, 80, 82, 84, 86, 87, 88, 89, 90, 90.5, 91, 91.4],
        isImproving: true
      }
    ];

    // Continuous Improvement / Innovation Metrics
    const continuousImprovementMetrics = [
      {
        id: "improvement-ideas",
        title: "Improvement Ideas",
        value: "127",
        unit: "ideas",
        targetPercentage: 35.1,
        trend: [80, 85, 90, 95, 100, 105, 110, 115, 120, 123, 125, 127],
        isImproving: true
      },
      {
        id: "implementation-rate",
        title: "Implementation Rate",
        value: "73.8",
        unit: "%",
        targetPercentage: 23.5,
        trend: [55, 58, 61, 64, 66, 68, 70, 71, 72, 73, 73.5, 73.8],
        isImproving: true
      },
      {
        id: "innovation-index",
        title: "Innovation Index",
        value: "68.2",
        unit: "index",
        targetPercentage: 28.3,
        trend: [45, 48, 51, 54, 57, 60, 62, 64, 66, 67, 67.5, 68.2],
        isImproving: true
      },
      {
        id: "cost-savings",
        title: "Cost Savings",
        value: "2.4M",
        unit: "USD",
        targetPercentage: 44.0,
        trend: [1.2, 1.4, 1.6, 1.8, 2.0, 2.1, 2.2, 2.25, 2.3, 2.35, 2.38, 2.4],
        isImproving: true
      }
    ];

    return {
      safetyMetrics,
      qualityMetrics,
      environmentalMetrics,
      riskManagementMetrics,
      operationalDisciplineMetrics,
      processReliabilityMetrics,
      complianceMetrics,
      organizationalCompetenceMetrics,
      leadershipEngagementMetrics,
      employeeEngagementMetrics,
      supplyChainMetrics,
      crisisPreparednessMetrics,
      digitalMaturityMetrics,
      continuousImprovementMetrics
    };
  };

  const {
    safetyMetrics,
    qualityMetrics,
    environmentalMetrics,
    riskManagementMetrics,
    operationalDisciplineMetrics,
    processReliabilityMetrics,
    complianceMetrics,
    organizationalCompetenceMetrics,
    leadershipEngagementMetrics,
    employeeEngagementMetrics,
    supplyChainMetrics,
    crisisPreparednessMetrics,
    digitalMaturityMetrics,
    continuousImprovementMetrics
  } = getPerformanceMetrics();

  // Performance sections configuration for easy maintenance and API integration
  const performanceSections = [
    {
      id: 'safety',
      title: 'Safety Performance',
      description: 'Key performance indicators to track overall safety performance of the operating entity.',
      metrics: safetyMetrics
    },
    {
      id: 'quality',
      title: 'Quality Performance',
      description: 'Indicators reflecting product and process quality across operations.',
      metrics: qualityMetrics
    },
    {
      id: 'environmental',
      title: 'Environmental Performance',
      description: 'Performance insights on environmental impact and sustainability efforts.',
      metrics: environmentalMetrics
    },
    {
      id: 'risk-management',
      title: 'Risk Management Effectiveness',
      description: 'Overview of how effectively risks are identified, managed, and mitigated.',
      metrics: riskManagementMetrics
    },
    {
      id: 'operational-discipline',
      title: 'Operational Discipline',
      description: 'Measures reflecting adherence to procedures and task execution discipline.',
      metrics: operationalDisciplineMetrics
    },
    {
      id: 'process-reliability',
      title: 'Process Reliability & Efficiency',
      description: 'Performance trends on consistency, efficiency, and operational throughput.',
      metrics: processReliabilityMetrics
    },
    {
      id: 'compliance',
      title: 'Compliance & Regulatory Adherence',
      description: 'Snapshot of compliance health and regulatory adherence across functions.',
      metrics: complianceMetrics
    },
    {
      id: 'organizational-competence',
      title: 'Organizational Competence & Learning',
      description: 'Insights into workforce capability, training effectiveness, and learning culture.',
      metrics: organizationalCompetenceMetrics
    },
    {
      id: 'leadership-engagement',
      title: 'Leadership Engagement & Culture',
      description: 'Indicators reflecting leadership visibility, engagement, and cultural alignment.',
      metrics: leadershipEngagementMetrics
    },
    {
      id: 'employee-engagement',
      title: 'Employee Engagement & Wellbeing',
      description: 'Trends in workforce participation, sentiment, and wellbeing.',
      metrics: employeeEngagementMetrics
    },
    {
      id: 'supply-chain',
      title: 'Supply Chain & Contractor Performance',
      description: 'Performance overview of external vendors and contractor contributions.',
      metrics: supplyChainMetrics
    },
    {
      id: 'crisis-preparedness',
      title: 'Crisis Preparedness & Business Continuity',
      description: 'Readiness metrics related to emergency response and business resilience.',
      metrics: crisisPreparednessMetrics
    },
    {
      id: 'digital-maturity',
      title: 'Digital Maturity & Automation',
      description: 'Progress on digital transformation, technology adoption, and automation.',
      metrics: digitalMaturityMetrics
    },
    {
      id: 'continuous-improvement',
      title: 'Continuous Improvement / Innovation',
      description: 'Indicators of improvement culture, innovation uptake, and idea execution.',
      metrics: continuousImprovementMetrics
    }
  ];

  return (
    <>
      <PageHeader
        title=" Dashboard"
        description="Your Central Hub for Data and Insights."
      />

      {/* Global Location Filter */}
      <div className="flex justify-start mb-6">
        <LocationFilterDropdown onFilterChange={handleLocationFilterChange} />
      </div>

      {/* Performance Sections */}
      <div className="space-y-12">
        {performanceSections.map((section) => (
          <div key={section.id} className="space-y-6">
            <div className="flex flex-col space-y-2">
              <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
              <p className="text-gray-600">{section.description}</p>
            </div>

            {/* Metrics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {section.metrics.map((metric) => (
                <MetricCard
                  key={metric.id}
                  {...metric}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default HomePage;
