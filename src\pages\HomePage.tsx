import { useState } from "react";
import PageHeader from "@/components/common/PageHeader";
import LocationFilterDropdown from "@/components/common/LocationFilterDropdown";
import MetricCard from "@/components/Charts/MetricCard";
import { LocationFilterState } from "@/types/locationFilter";
import { Shield, AlertTriangle, Clock, CheckCircle } from "lucide-react";


const HomePage = () => {
  // Location filter state
  const [locationFilters, setLocationFilters] = useState<LocationFilterState>({
    locationOne: '',
    locationTwo: '',
    locationThree: '',
    locationFour: '',
    locationFive: '',
    locationSix: '',
  });

  // Handle location filter changes
  const handleLocationFilterChange = (filters: LocationFilterState) => {
    setLocationFilters(filters);
    // Here you can add logic to filter data based on location
    console.log('Location filters applied:', filters);
  };

  // Safety Performance Metrics
  const safetyMetrics = [
    {
      id: "safety-incidents",
      title: "Safety Incidents",
      value: "2",
      unit: "incidents",
      targetPercentage: -15.2,
      trend: [8, 6, 5, 4, 3, 2, 2, 2, 2, 2, 2, 2],
      isImproving: true
    },
    {
      id: "days-without-incident",
      title: "Days Without Incident",
      value: "127",
      unit: "days",
      targetPercentage: 12.5,
      trend: [45, 67, 89, 102, 115, 127, 127, 127, 127, 127, 127, 127],
      isImproving: true
    },
    {
      id: "safety-training-completion",
      title: "Safety Training Completion",
      value: "94.8",
      unit: "%",
      targetPercentage: 5.3,
      trend: [85, 87, 89, 91, 92, 93, 94, 94.2, 94.5, 94.6, 94.7, 94.8],
      isImproving: true
    },
    {
      id: "near-miss-reports",
      title: "Near Miss Reports",
      value: "18",
      unit: "reports",
      targetPercentage: 28.6,
      trend: [8, 10, 12, 14, 15, 16, 17, 17, 18, 18, 18, 18],
      isImproving: true
    }
  ];

  return (
    <>
      <PageHeader
        title=" Dashboard"
        description="Your Central Hub for Data and Insights."
      />

      {/* Global Location Filter */}
      <div className="flex justify-start mb-6">
        <LocationFilterDropdown onFilterChange={handleLocationFilterChange} />
      </div>

      {/* Safety Performance Section */}
      <div className="space-y-6">
        <div className="flex flex-col space-y-2">
          <h2 className="text-2xl font-bold text-gray-900">Safety Performance</h2>
          <p className="text-gray-600">Key performance indicators to track overall safety performance of the operating entity.</p>
        </div>

        {/* Safety Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {safetyMetrics.map((metric) => (
            <MetricCard
              key={metric.id}
              {...metric}
            />
          ))}
        </div>
      </div>
    </>
  );
};

export default HomePage;
