import { useState } from "react";
import PageHeader from "@/components/common/PageHeader";
import LocationFilterDropdown from "@/components/common/LocationFilterDropdown";
import MetricCard from "@/components/Charts/MetricCard";
import { LocationFilterState } from "@/types/locationFilter";
import { Shield, AlertTriangle, Clock, CheckCircle, Target, TrendingUp, Award, BarChart3 } from "lucide-react";


const HomePage = () => {
  // Location filter state
  const [locationFilters, setLocationFilters] = useState<LocationFilterState>({
    locationOne: '',
    locationTwo: '',
    locationThree: '',
    locationFour: '',
    locationFive: '',
    locationSix: '',
  });

  // Handle location filter changes
  const handleLocationFilterChange = (filters: LocationFilterState) => {
    setLocationFilters(filters);
    // Here you can add logic to filter data based on location
    console.log('Location filters applied:', filters);
  };

  // Performance Metrics Data - Structured for easy API integration
  const getPerformanceMetrics = () => {
    // Safety Performance Metrics
    const safetyMetrics = [
      {
        id: "safety-incidents",
        title: "Safety Incidents",
        value: "2",
        unit: "incidents",
        targetPercentage: -15.2,
        trend: [8, 6, 5, 4, 3, 2, 2, 2, 2, 2, 2, 2],
        isImproving: true
      },
      {
        id: "days-without-incident",
        title: "Days Without Incident",
        value: "127",
        unit: "days",
        targetPercentage: 12.5,
        trend: [45, 67, 89, 102, 115, 127, 127, 127, 127, 127, 127, 127],
        isImproving: true
      },
      {
        id: "safety-training-completion",
        title: "Safety Training Completion",
        value: "94.8",
        unit: "%",
        targetPercentage: 5.3,
        trend: [85, 87, 89, 91, 92, 93, 94, 94.2, 94.5, 94.6, 94.7, 94.8],
        isImproving: true
      },
      {
        id: "near-miss-reports",
        title: "Near Miss Reports",
        value: "18",
        unit: "reports",
        targetPercentage: 28.6,
        trend: [8, 10, 12, 14, 15, 16, 17, 17, 18, 18, 18, 18],
        isImproving: true
      }
    ];

    // Quality Performance Metrics
    const qualityMetrics = [
      {
        id: "first-pass-yield",
        title: "First Pass Yield",
        value: "96.7",
        unit: "%",
        targetPercentage: 8.2,
        trend: [89, 91, 92, 93, 94, 95, 95.5, 96, 96.2, 96.4, 96.6, 96.7],
        isImproving: true
      },
      {
        id: "defect-rate",
        title: "Defect Rate",
        value: "0.8",
        unit: "%",
        targetPercentage: -23.1,
        trend: [2.1, 1.9, 1.7, 1.5, 1.3, 1.1, 1.0, 0.9, 0.85, 0.82, 0.81, 0.8],
        isImproving: true
      },
      {
        id: "customer-satisfaction",
        title: "Customer Satisfaction",
        value: "4.6",
        unit: "/5.0",
        targetPercentage: 15.0,
        trend: [4.0, 4.1, 4.2, 4.2, 4.3, 4.4, 4.4, 4.5, 4.5, 4.6, 4.6, 4.6],
        isImproving: true
      },
      {
        id: "process-capability",
        title: "Process Capability (Cpk)",
        value: "1.67",
        unit: "index",
        targetPercentage: 11.3,
        trend: [1.2, 1.3, 1.35, 1.4, 1.45, 1.5, 1.55, 1.6, 1.62, 1.65, 1.66, 1.67],
        isImproving: true
      }
    ];

    return { safetyMetrics, qualityMetrics };
  };

  const { safetyMetrics, qualityMetrics } = getPerformanceMetrics();

  return (
    <>
      <PageHeader
        title=" Dashboard"
        description="Your Central Hub for Data and Insights."
      />

      {/* Global Location Filter */}
      <div className="flex justify-start mb-6">
        <LocationFilterDropdown onFilterChange={handleLocationFilterChange} />
      </div>

      {/* Performance Sections */}
      <div className="space-y-12">
        {/* Safety Performance Section */}
        <div className="space-y-6">
          <div className="flex flex-col space-y-2">
            <h2 className="text-2xl font-bold text-gray-900">Safety Performance</h2>
            <p className="text-gray-600">Key performance indicators to track overall safety performance of the operating entity.</p>
          </div>

          {/* Safety Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {safetyMetrics.map((metric) => (
              <MetricCard
                key={metric.id}
                {...metric}
              />
            ))}
          </div>
        </div>

        {/* Quality Performance Section */}
        <div className="space-y-6">
          <div className="flex flex-col space-y-2">
            <h2 className="text-2xl font-bold text-gray-900">Quality Performance</h2>
            <p className="text-gray-600">Indicators reflecting product and process quality across operations.</p>
          </div>

          {/* Quality Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {qualityMetrics.map((metric) => (
              <MetricCard
                key={metric.id}
                {...metric}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default HomePage;
