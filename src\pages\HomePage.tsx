import { useState } from "react";
import PageHeader from "@/components/common/PageHeader";
import LocationFilterDropdown from "@/components/common/LocationFilterDropdown";
import { LocationFilterState } from "@/types/locationFilter";


const HomePage = () => {
  // Location filter state
  const [locationFilters, setLocationFilters] = useState<LocationFilterState>({
    locationOne: '',
    locationTwo: '',
    locationThree: '',
    locationFour: '',
    locationFive: '',
    locationSix: '',
  });

  // Handle location filter changes
  const handleLocationFilterChange = (filters: LocationFilterState) => {
    setLocationFilters(filters);
    // Here you can add logic to filter data based on location
    console.log('Location filters applied:', filters);
  };

  return (
    <>
      <PageHeader
        title=" Dashboard"
        description="Your Central Hub for Data and Insights."
      />

      {/* Global Location Filter */}
      <div className="flex justify-start mb-6">
        <LocationFilterDropdown onFilterChange={handleLocationFilterChange} />
      </div>

      {/* Charts and tabs removed - logic will be changed */}
    </>
  );
};

export default HomePage;
